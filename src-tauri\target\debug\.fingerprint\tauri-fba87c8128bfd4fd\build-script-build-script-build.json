{"rustc": 16591470773350601817, "features": "[\"common-controls-v6\", \"compression\", \"default\", \"objc-exception\", \"tauri-runtime-wry\", \"wry\"]", "declared_features": "[\"common-controls-v6\", \"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"data-url\", \"default\", \"devtools\", \"http-range\", \"image\", \"image-ico\", \"image-png\", \"isolation\", \"linux-libxdo\", \"macos-private-api\", \"macos-proxy\", \"native-tls\", \"native-tls-vendored\", \"objc-exception\", \"process-relaunch-dangerous-allow-symlink-macos\", \"protocol-asset\", \"rustls-tls\", \"specta\", \"tauri-runtime-wry\", \"test\", \"tracing\", \"tray-icon\", \"unstable\", \"uuid\", \"webview-data-url\", \"wry\"]", "target": 5408242616063297496, "profile": 2225463790103693989, "path": 18200840995953530386, "deps": [[7620173704795778974, "tauri_build", false, 11121961815655420887], [10904757748148156433, "tauri_utils", false, 14206726882084026176], [13077543566650298139, "heck", false, 9067240254108026350], [17155886227862585100, "glob", false, 12090592369537269039]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-fba87c8128bfd4fd\\dep-build-script-build-script-build", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}
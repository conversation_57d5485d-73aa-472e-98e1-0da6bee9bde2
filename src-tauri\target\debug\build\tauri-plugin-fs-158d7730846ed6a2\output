cargo:rerun-if-changed=permissions
cargo:PERMISSION_FILES_PATH=C:\playground\Tauri\todo2\src-tauri\target\debug\build\tauri-plugin-fs-158d7730846ed6a2\out\tauri-plugin-fs-permission-files
cargo:GLOBAL_SCOPE_SCHEMA_PATH=C:\playground\Tauri\todo2\src-tauri\target\debug\build\tauri-plugin-fs-158d7730846ed6a2\out\global-scope.json
cargo:GLOBAL_API_SCRIPT_PATH=\\?\C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\tauri-plugin-fs-2.2.0\api-iife.js
cargo:rustc-check-cfg=cfg(mobile)
cargo:rustc-check-cfg=cfg(desktop)
cargo:rustc-cfg=desktop

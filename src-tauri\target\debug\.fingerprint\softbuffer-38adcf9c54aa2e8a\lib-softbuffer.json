{"rustc": 16591470773350601817, "features": "[]", "declared_features": "[\"as-raw-xcb-connection\", \"bytemuck\", \"default\", \"drm\", \"fastrand\", \"kms\", \"memmap2\", \"rustix\", \"tiny-xlib\", \"wayland\", \"wayland-backend\", \"wayland-client\", \"wayland-dlopen\", \"wayland-sys\", \"x11\", \"x11-dlopen\", \"x11rb\"]", "target": 9174284484934603102, "profile": 15657897354478470176, "path": 7546407106411770624, "deps": [[376837177317575824, "build_script_build", false, 13549983468824288479], [385810070298638530, "log", false, 12830837629140526522], [4143744114649553716, "raw_window_handle", false, 1658071671848349442], [10281541584571964250, "windows_sys", false, 8050641324212377675]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\softbuffer-38adcf9c54aa2e8a\\dep-lib-softbuffer", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}
{"rustc": 16591470773350601817, "features": "[]", "declared_features": "[\"devtools\", \"macos-private-api\"]", "target": 10306386172444932100, "profile": 15657897354478470176, "path": 15833117489958080037, "deps": [[411067296443658118, "serde", false, 7268641657127311826], [828094305214142069, "http", false, 11954631078804817087], [2452088868158763674, "thiserror", false, 9406192013254268985], [3150220818285335163, "url", false, 1668980580842094902], [4143744114649553716, "raw_window_handle", false, 1658071671848349442], [5520207325003309242, "build_script_build", false, 12371550460531702444], [8854056551936332045, "dpi", false, 1052420291505931209], [9887204924812407586, "serde_json", false, 14048371504091381425], [10904757748148156433, "tauri_utils", false, 1834608709070416377], [13160085343524438957, "windows", false, 8417943904540847359]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-runtime-e605dbba81681d68\\dep-lib-tauri_runtime", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}
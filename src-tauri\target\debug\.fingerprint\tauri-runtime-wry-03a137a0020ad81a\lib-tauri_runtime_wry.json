{"rustc": 16591470773350601817, "features": "[\"objc-exception\"]", "declared_features": "[\"devtools\", \"macos-private-api\", \"macos-proxy\", \"objc-exception\", \"tracing\", \"unstable\"]", "target": 1901661049345253480, "profile": 15657897354478470176, "path": 7504785885069649157, "deps": [[143152310456438171, "tao", false, 15118126923030266473], [376837177317575824, "softbuffer", false, 5110821935040612744], [385810070298638530, "log", false, 12830837629140526522], [828094305214142069, "http", false, 11954631078804817087], [962946812575324851, "build_script_build", false, 16186705635981895318], [3150220818285335163, "url", false, 1668980580842094902], [4143744114649553716, "raw_window_handle", false, 1658071671848349442], [5520207325003309242, "tauri_runtime", false, 378370139858561591], [10585299166367638045, "wry", false, 16437916386473337337], [10904757748148156433, "tauri_utils", false, 1834608709070416377], [13160085343524438957, "windows", false, 8417943904540847359], [13172307528254670878, "webview2_com", false, 2781091869350331365]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-runtime-wry-03a137a0020ad81a\\dep-lib-tauri_runtime_wry", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}
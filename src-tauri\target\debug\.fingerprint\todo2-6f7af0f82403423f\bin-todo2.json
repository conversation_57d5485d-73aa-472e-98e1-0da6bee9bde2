{"rustc": 16591470773350601817, "features": "[]", "declared_features": "[\"custom-protocol\"]", "target": 13916452019731651957, "profile": 17672942494452627365, "path": 4942398508502643691, "deps": [[411067296443658118, "serde", false, 4061225250186610121], [2528587820129791182, "directories", false, 4930867507631208615], [6183408989399188640, "tauri", false, 7464878326306581394], [6698410659470529676, "tauri_plugin_opener", false, 5646980935772321388], [8657334605283922225, "tauri_plugin_dialog", false, 14447729882399688879], [9887204924812407586, "serde_json", false, 947440964930829139], [10799047760602221123, "todo2_lib", false, 4008327570524430398], [10799047760602221123, "build_script_build", false, 1097210862579211066]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\todo2-6f7af0f82403423f\\dep-bin-todo2", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}
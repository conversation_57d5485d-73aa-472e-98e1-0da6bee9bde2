{"rustc": 16591470773350601817, "features": "[\"brotli\", \"compression\", \"resources\", \"walkdir\"]", "declared_features": "[\"aes-gcm\", \"brotli\", \"build\", \"cargo_metadata\", \"compression\", \"config-json5\", \"config-toml\", \"getrandom\", \"isolation\", \"json5\", \"proc-macro2\", \"process-relaunch-dangerous-allow-symlink-macos\", \"quote\", \"resources\", \"schema\", \"schemars\", \"serialize-to-javascript\", \"swift-rs\", \"walkdir\"]", "target": 7530130812022395703, "profile": 15657897354478470176, "path": 7881910854656440442, "deps": [[385810070298638530, "log", false, 12830837629140526522], [411067296443658118, "serde", false, 7268641657127311826], [539018850899343221, "semver", false, 152384604976233995], [561782849581144631, "html5ever", false, 14004401477037170538], [828094305214142069, "http", false, 11954631078804817087], [1200537532907108615, "url<PERSON><PERSON>n", false, 1850598362349574216], [2442447106484478337, "uuid", false, 2993149602043817435], [2452088868158763674, "thiserror", false, 9406192013254268985], [3129130049864710036, "memchr", false, 13993755827556866357], [3150220818285335163, "url", false, 1668980580842094902], [6213549728662707793, "serde_with", false, 5372375151820609919], [6262254372177975231, "kuchiki", false, 5147295058489367326], [6606131838865521726, "ctor", false, 792534237554945071], [7170110829644101142, "json_patch", false, 2127256540428286371], [9074807186317717791, "serde_untagged", false, 16354122222337909024], [9241165288213079952, "toml", false, 8274503137442080947], [9451456094439810778, "regex", false, 11589257327167655412], [9887204924812407586, "serde_json", false, 14048371504091381425], [11989259058781683633, "dunce", false, 9510001237055968877], [12428537176704437079, "infer", false, 4174176736235288932], [14132538657330703225, "brotli", false, 1115293256320128295], [15622660310229662834, "walkdir", false, 951117218890240745], [17155886227862585100, "glob", false, 12090592369537269039], [17186037756130803222, "phf", false, 12999322523195561166]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-utils-2b156f8584646b3a\\dep-lib-tauri_utils", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}
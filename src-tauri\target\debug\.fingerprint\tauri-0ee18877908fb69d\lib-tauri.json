{"rustc": 16591470773350601817, "features": "[\"common-controls-v6\", \"compression\", \"default\", \"objc-exception\", \"tauri-runtime-wry\", \"wry\"]", "declared_features": "[\"common-controls-v6\", \"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"data-url\", \"default\", \"devtools\", \"http-range\", \"image\", \"image-ico\", \"image-png\", \"isolation\", \"linux-libxdo\", \"macos-private-api\", \"macos-proxy\", \"native-tls\", \"native-tls-vendored\", \"objc-exception\", \"process-relaunch-dangerous-allow-symlink-macos\", \"protocol-asset\", \"rustls-tls\", \"specta\", \"tauri-runtime-wry\", \"test\", \"tracing\", \"tray-icon\", \"unstable\", \"uuid\", \"webview-data-url\", \"wry\"]", "target": 12223948975794516716, "profile": 15657897354478470176, "path": 15554459628508205217, "deps": [[40386456601120721, "percent_encoding", false, 1640642462888934309], [385810070298638530, "log", false, 12830837629140526522], [411067296443658118, "serde", false, 7268641657127311826], [721665158612673359, "anyhow", false, 8019839401977583556], [828094305214142069, "http", false, 11954631078804817087], [962946812575324851, "tauri_runtime_wry", false, 5297497361557053308], [1200537532907108615, "url<PERSON><PERSON>n", false, 1850598362349574216], [1861048441542724925, "bytes", false, 7190673609887988782], [2011830238986063773, "tokio", false, 10641095768516249039], [2452088868158763674, "thiserror", false, 9406192013254268985], [2999978091831213404, "serde_repr", false, 106032449761912463], [3150220818285335163, "url", false, 1668980580842094902], [4143744114649553716, "raw_window_handle", false, 1658071671848349442], [4725822363599429376, "muda", false, 13342304710103744834], [4919829919303820331, "serialize_to_javascript", false, 13710537012087597674], [5520207325003309242, "tauri_runtime", false, 378370139858561591], [6183408989399188640, "build_script_build", false, 10484872313046629081], [7670211519503158651, "getrandom", false, 3352293817481673653], [8256202458064874477, "dirs", false, 3419519696918696032], [9276336542470079068, "window_vibrancy", false, 4226945942462129536], [9813213285881231547, "reqwest", false, 7829210290033438663], [9887204924812407586, "serde_json", false, 14048371504091381425], [10229185211513642314, "mime", false, 803608100333810541], [10629569228670356391, "futures_util", false, 563132518298230412], [10904757748148156433, "tauri_utils", false, 1834608709070416377], [11989259058781683633, "dunce", false, 9510001237055968877], [13077543566650298139, "heck", false, 9067240254108026350], [13160085343524438957, "windows", false, 8417943904540847359], [13172307528254670878, "webview2_com", false, 2781091869350331365], [17155886227862585100, "glob", false, 12090592369537269039], [18353508673230037392, "tauri_macros", false, 435053289510641716]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-0ee18877908fb69d\\dep-lib-tauri", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}
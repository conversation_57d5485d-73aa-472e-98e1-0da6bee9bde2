{"rustc": 16591470773350601817, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[6183408989399188640, "build_script_build", false, 10484872313046629081], [8657334605283922225, "build_script_build", false, 8148983026371127539], [6698410659470529676, "build_script_build", false, 18428338471485238316], [10799047760602221123, "build_script_build", false, 11743440750655065672]], "local": [{"RerunIfChanged": {"output": "debug\\build\\todo2-8346683c2bb4bf75\\output", "paths": ["tauri.conf.json", "capabilities"]}}, {"RerunIfEnvChanged": {"var": "TAURI_CONFIG", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}
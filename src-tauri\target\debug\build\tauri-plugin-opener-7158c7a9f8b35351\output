cargo:rerun-if-changed=permissions
cargo:PERMISSION_FILES_PATH=C:\playground\Tauri\todo2\src-tauri\target\debug\build\tauri-plugin-opener-7158c7a9f8b35351\out\tauri-plugin-opener-permission-files
cargo:GLOBAL_SCOPE_SCHEMA_PATH=C:\playground\Tauri\todo2\src-tauri\target\debug\build\tauri-plugin-opener-7158c7a9f8b35351\out\global-scope.json
cargo:GLOBAL_API_SCRIPT_PATH=\\?\C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\tauri-plugin-opener-2.2.4\api-iife.js
cargo:rustc-check-cfg=cfg(mobile)
cargo:rustc-check-cfg=cfg(desktop)
cargo:rustc-cfg=desktop
cargo:rustc-check-cfg=cfg(desktop)
cargo:rustc-cfg=desktop
cargo:rustc-check-cfg=cfg(mobile)
